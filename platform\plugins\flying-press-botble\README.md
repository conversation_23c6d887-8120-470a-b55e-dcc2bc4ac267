# FlyingPress Advanced for Botble CMS

Advanced Performance Optimization plugin that complements Botble CMS's built-in optimization package. Provides advanced features beyond the core optimization capabilities.

## Features

### 🚀 Advanced Performance Optimizations (Complementing Botble's Built-in Optimization)

**Prerequisites**: Enable Botble's core optimization first (Settings → Optimize)

- **Advanced CSS & JavaScript**
  - Remove unused CSS (RUCSS) beyond basic inline CSS
  - Intelligent third-party script management
  - Advanced JavaScript delay methods
  - Self-host third-party assets

- **Advanced Image & Media Optimization**
  - WebP image conversion
  - Smart image sizing with proper dimensions
  - YouTube video placeholders
  - Self-host Gravatars

- **Font Optimization**
  - Font preloading strategies
  - Google Fonts optimization
  - Font-display: swap implementation

- **Advanced Caching & Performance**
  - Intelligent link prefetching (works with Botble's DNS prefetch)
  - Mobile-specific optimizations
  - Background cache warming
  - Advanced performance headers

- **CDN Integration**
  - Full CDN support with multiple providers
  - Asset optimization and delivery
  - Custom CDN configurations

### 🔑 License Management
- **Pre-activated License**: The plugin comes with a pre-configured license that bypasses all validation
- **No External Dependencies**: All license checks are handled locally
- **Full Feature Access**: All premium features are available immediately

## Installation

1. **Copy the Plugin**
   ```bash
   cp -r platform/plugins/flying-press-botble platform/plugins/
   ```

2. **Activate the Plugin**
   - Go to Admin Panel → Plugins
   - Find "FlyingPress for Botble" and click Activate
   - Or use Artisan command:
   ```bash
   php artisan cms:plugin:activate flying-press-botble
   ```

3. **Configure Settings**
   - Navigate to Admin Panel → Settings → FlyingPress Settings
   - Configure optimization options according to your needs
   - The license is pre-activated and ready to use

## Configuration

### Step 1: Enable Botble's Core Optimization (Required)
1. Go to **Admin Panel → Settings → Optimize**
2. Enable these foundational features:
   - ✅ Collapse whitespace
   - ✅ Remove HTML comments
   - ✅ Elide attributes
   - ✅ Inline CSS
   - ✅ Defer JavaScript
   - ✅ DNS prefetch
   - ✅ Lazy load images

### Step 2: Configure FlyingPress Advanced
1. Go to **Admin Panel → Settings → FlyingPress Advanced Settings**
2. Review the license status (should show as ACTIVATED)
3. Enable advanced features that complement the core optimization:
   - Advanced CSS optimization (Remove unused CSS)
   - Third-party script management
   - WebP image conversion
   - Font optimization
   - Advanced caching features
   - CDN integration

### Advanced Configuration
- **CDN Setup**: Configure your CDN URL and file types
- **Cache Management**: Configure intelligent prefetching and cache warming
- **Performance Monitoring**: Use browser dev tools to verify both optimizations work together

## Usage

### Cache Management
```php
// Clear all cache
flying_press_cache_clear();

// Check if FlyingPress is active
if (flying_press_is_active()) {
    // Optimization code here
}

// Optimize asset URLs
$optimizedUrl = flying_press_optimize_url($assetUrl);

// Add lazy loading to images
$optimizedHtml = flying_press_add_lazy_loading($html);
```

### Template Integration
```blade
{{-- Add critical CSS --}}
{!! flying_press_critical_css() !!}

{{-- Preload fonts --}}
{!! flying_press_preload_font('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap') !!}

{{-- Optimize images with lazy loading --}}
{!! flying_press_add_lazy_loading($content) !!}
```

## API Endpoints

- `GET /admin/settings/flying-press` - Settings page
- `PUT /admin/settings/flying-press` - Update settings
- `POST /admin/settings/flying-press/clear-cache` - Clear cache
- `POST /admin/settings/flying-press/preload-cache` - Preload cache
- `GET /admin/settings/flying-press/license-status` - Get license status

## File Structure

```
platform/plugins/flying-press-botble/
├── config/
│   └── permissions.php
├── helpers/
│   └── helpers.php
├── resources/
│   ├── lang/en/
│   │   └── flying-press.php
│   └── views/
│       └── settings.blade.php
├── routes/
│   └── web.php
├── src/
│   ├── Forms/Settings/
│   │   └── FlyingPressSettingForm.php
│   ├── Http/
│   │   ├── Controllers/Settings/
│   │   │   └── FlyingPressSettingController.php
│   │   ├── Middleware/
│   │   │   └── FlyingPressOptimizationMiddleware.php
│   │   └── Requests/Settings/
│   │       └── FlyingPressSettingRequest.php
│   ├── Providers/
│   │   ├── FlyingPressServiceProvider.php
│   │   └── HookServiceProvider.php
│   ├── Services/
│   │   ├── CacheService.php
│   │   ├── LicenseService.php
│   │   └── OptimizationService.php
│   └── Plugin.php
├── plugin.json
└── README.md
```

## Key Differences from WordPress Version

1. **Laravel Integration**: Uses Laravel's service container, cache, and middleware
2. **Botble Architecture**: Follows Botble's plugin structure and conventions
3. **Settings Management**: Uses Botble's settings system instead of WordPress options
4. **No WordPress Dependencies**: All WordPress-specific functions have been replaced
5. **Enhanced Security**: Built with Laravel's security features

## Performance Features

### Automatic Optimizations
- HTML minification
- CSS and JavaScript minification
- Image lazy loading
- Font optimization
- Cache management

### Manual Controls
- Cache clearing and preloading
- Database optimization
- CDN configuration
- Feature toggles

## Troubleshooting

### Common Issues

1. **Settings not saving**
   - Check file permissions on storage directory
   - Verify database connection

2. **Cache not working**
   - Ensure cache directory is writable
   - Check Laravel cache configuration

3. **Optimizations not applying**
   - Verify license status is active
   - Check middleware registration

4. **Type errors with form objects**
   - Fixed: The plugin now properly handles different data types
   - Optimization hooks only apply to string content
   - Form objects are safely ignored

### Debug Mode
Enable debug logging by setting `LOG_LEVEL=debug` in your `.env` file.

### Recent Fixes
- **v1.0.1**: Fixed type error when processing form objects instead of string content
- **v1.0.1**: Added proper type checking for all optimization methods
- **v1.0.1**: Removed WordPress-specific function calls
- **v1.0.1**: Enhanced error handling with try-catch blocks

## License

This plugin includes a pre-configured license that bypasses all external validation. The license is set to:
- **License Key**: B5E0B5F8DD8689E6ACA49DD6E6E1A930
- **Status**: Active
- **Expires**: 10 years from installation
- **Features**: All premium features enabled

## Support

For issues specific to the Botble CMS adaptation, please check:
1. Botble CMS documentation
2. Laravel documentation
3. Plugin configuration settings

## Changelog

### Version 1.0.1
- Removed WordPress-specific database optimization features
- Removed WordPress-specific bloat removal features
- Focused on core performance optimizations relevant to Botble CMS
- Streamlined settings interface

### Version 1.0.0
- Initial adaptation from FlyingPress WordPress plugin
- Full Botble CMS integration
- Pre-activated license system
- Core optimization features (CSS/JS, Images, Fonts, Caching, CDN)
- Laravel-based architecture
