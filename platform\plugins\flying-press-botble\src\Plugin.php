<?php

namespace Shaqi\FlyingPress;

use <PERSON><PERSON><PERSON>\PluginManagement\Abstracts\PluginOperationAbstract;
use <PERSON><PERSON><PERSON>\Setting\Facades\Setting;
use Illuminate\Support\Facades\File;

class Plugin extends PluginOperationAbstract
{
    public static function activated(): void
    {
        // Set default configuration when plugin is activated
        $defaultConfig = [
            // License - pre-configured with valid license
            'flying_press_license_key' => 'B5E0B5F8DD8689E6ACA49DD6E6E1A930',
            'flying_press_license_active' => true,
            'flying_press_license_status' => 'active',

            // Advanced CSS & JavaScript Optimization (complementing Botble's built-in)
            'flying_press_css_rucss' => false,
            'flying_press_js_delay_method' => 'interaction',
            'flying_press_js_delay_third_party' => false,
            'flying_press_css_js_self_host_third_party' => false,

            // Advanced Image & Media Optimization (beyond basic lazy loading)
            'flying_press_properly_size_images' => false,
            'flying_press_youtube_placeholder' => false,
            'flying_press_self_host_gravatars' => false,
            'flying_press_webp_conversion' => false,

            // Font Optimization
            'flying_press_fonts_preload' => false,
            'flying_press_fonts_optimize_google' => false,
            'flying_press_fonts_display_swap' => false,

            // Advanced Caching & Performance
            'flying_press_cache_link_prefetch' => false,
            'flying_press_cache_mobile' => false,
            'flying_press_cache_logged_in' => false,
            'flying_press_cache_refresh' => false,
            'flying_press_cache_refresh_interval' => '6hours',

            // CDN Integration
            'flying_press_cdn' => false,
            'flying_press_cdn_type' => 'custom',
            'flying_press_cdn_url' => '',
            'flying_press_cdn_file_types' => 'all',
        ];

        foreach ($defaultConfig as $key => $value) {
            if (!Setting::has($key)) {
                Setting::set($key, $value);
            }
        }

        Setting::save();

        // Create cache directory
        $cacheDir = storage_path('app/flying-press-cache');
        if (!File::exists($cacheDir)) {
            File::makeDirectory($cacheDir, 0755, true);
        }
    }

    public static function remove(): void
    {
        // Remove all FlyingPress settings
        $settingsToRemove = [
            'flying_press_license_key',
            'flying_press_license_active',
            'flying_press_license_status',
            'flying_press_css_rucss',
            'flying_press_js_delay_method',
            'flying_press_js_delay_third_party',
            'flying_press_css_js_self_host_third_party',
            'flying_press_properly_size_images',
            'flying_press_youtube_placeholder',
            'flying_press_self_host_gravatars',
            'flying_press_webp_conversion',
            'flying_press_fonts_preload',
            'flying_press_fonts_optimize_google',
            'flying_press_fonts_display_swap',
            'flying_press_cache_link_prefetch',
            'flying_press_cache_mobile',
            'flying_press_cache_logged_in',
            'flying_press_cache_refresh',
            'flying_press_cache_refresh_interval',
            'flying_press_cdn',
            'flying_press_cdn_type',
            'flying_press_cdn_url',
            'flying_press_cdn_file_types',
        ];

        Setting::delete($settingsToRemove);

        // Remove cache directory
        $cacheDir = storage_path('app/flying-press-cache');
        if (File::exists($cacheDir)) {
            File::deleteDirectory($cacheDir);
        }
    }
}
