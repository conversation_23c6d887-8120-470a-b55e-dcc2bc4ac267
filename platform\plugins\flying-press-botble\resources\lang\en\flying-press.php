<?php

return [
    'name' => 'FlyingPress Advanced',
    'description' => 'Advanced Performance Optimization that complements Botble CMS built-in optimization',

    'settings' => [
        'title' => 'FlyingPress Advanced Settings',
        'description' => 'Configure advanced performance optimization that complements Botble\'s built-in optimization',
        'license' => [
            'title' => 'License Information',
            'status' => 'License Status',
            'key' => 'License Key',
            'active' => 'Active',
            'expires' => 'Expires',
            'activated_message' => 'License validated successfully! All premium features are active.',
        ],
        'optimization' => [
            'css_js' => [
                'title' => 'CSS & JavaScript Optimization',
                'minify' => 'Minify CSS & JavaScript',
                'minify_help' => 'Automatically minify CSS and JavaScript files to reduce file sizes',
                'remove_unused_css' => 'Remove Unused CSS',
                'remove_unused_css_help' => 'Remove unused CSS to reduce page size and improve loading speed',
                'delay_js' => 'Delay JavaScript Execution',
                'delay_js_help' => 'Delay non-critical JavaScript execution until user interaction',
                'delay_method' => 'JavaScript Delay Method',
                'delay_third_party' => 'Delay Third-Party JavaScript',
                'delay_third_party_help' => 'Delay third-party JavaScript like analytics, social widgets, etc.',
                'self_host_third_party' => 'Self-Host Third-Party Assets',
                'self_host_third_party_help' => 'Download and serve third-party CSS/JS files from your server',
            ],
            'images' => [
                'title' => 'Image & Media Optimization',
                'lazy_load' => 'Lazy Load Images',
                'lazy_load_help' => 'Load images only when they are about to enter the viewport',
                'properly_size' => 'Properly Size Images',
                'properly_size_help' => 'Automatically resize images to appropriate dimensions',
                'youtube_placeholder' => 'YouTube Video Placeholders',
                'youtube_placeholder_help' => 'Replace YouTube embeds with lightweight placeholders',
                'self_host_gravatars' => 'Self-Host Gravatars',
                'self_host_gravatars_help' => 'Download and serve Gravatar images from your server',
            ],
            'fonts' => [
                'title' => 'Font Optimization',
                'preload' => 'Preload Fonts',
                'preload_help' => 'Preload critical fonts to improve loading performance',
                'optimize_google' => 'Optimize Google Fonts',
                'optimize_google_help' => 'Optimize Google Fonts loading for better performance',
                'display_swap' => 'Font Display Swap',
                'display_swap_help' => 'Use font-display: swap for better perceived performance',
            ],
            'caching' => [
                'title' => 'Caching Settings',
                'link_prefetch' => 'Link Prefetching',
                'link_prefetch_help' => 'Prefetch pages when users hover over links',
                'mobile' => 'Separate Mobile Cache',
                'mobile_help' => 'Create separate cache files for mobile devices',
                'logged_in' => 'Cache for Logged-in Users',
                'logged_in_help' => 'Enable caching for logged-in users (use with caution)',
                'auto_refresh' => 'Automatic Cache Refresh',
                'auto_refresh_help' => 'Automatically refresh cache at specified intervals',
                'refresh_interval' => 'Cache Refresh Interval',
            ],
            'cdn' => [
                'title' => 'CDN Settings',
                'enable' => 'Enable CDN',
                'enable_help' => 'Enable Content Delivery Network for faster asset delivery',
                'type' => 'CDN Type',
                'url' => 'CDN URL',
                'url_help' => 'Enter your CDN URL (e.g., https://cdn.example.com)',
                'file_types' => 'CDN File Types',
            ],
        ],
        'cache_management' => [
            'title' => 'Cache Management',
            'clear_cache' => 'Clear All Cache',
            'preload_cache' => 'Preload Cache',
            'cache_cleared' => 'Cache cleared successfully!',
            'cache_preload_started' => 'Cache preloading started in background!',
        ],
    ],

    'messages' => [
        'settings_updated' => 'Settings updated successfully! Cache has been cleared.',
        'cache_cleared' => 'Cache cleared successfully!',
        'cache_preload_started' => 'Cache preloading started in background!',
        'license_active' => 'License is active and all features are available.',
    ],
];
