<?php

namespace <PERSON><PERSON>qi\FlyingPress\Providers;

use <PERSON>haqi\Base\Facades\DashboardMenu;
use <PERSON>haqi\Base\Supports\DashboardMenuItem;
use <PERSON>haqi\Base\Supports\ServiceProvider;
use <PERSON>haqi\Base\Traits\LoadAndPublishDataTrait;
use <PERSON><PERSON>qi\FlyingPress\Http\Middleware\FlyingPressOptimizationMiddleware;
use Shaqi\FlyingPress\Services\CacheService;
use Shaqi\FlyingPress\Services\OptimizationService;
use Shaqi\FlyingPress\Services\LicenseService;
use Shaqi\Setting\PanelSections\SettingOthersPanelSection;
use Shaqi\Base\Facades\PanelSectionManager;
use Shaqi\Base\PanelSections\PanelSectionItem;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\Facades\Event;

class FlyingPressServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        // Register services
        $this->app->singleton(CacheService::class);
        $this->app->singleton(OptimizationService::class);
        $this->app->singleton(LicenseService::class);
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/flying-press-botble')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadAndPublishTranslations()
            ->loadRoutes()
            ->loadAndPublishViews()
            ->publishAssets();

        // Register dashboard menu
        DashboardMenu::default()->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem(
                    DashboardMenuItem::make()
                        ->id('cms-plugins-flying-press')
                        ->priority(100)
                        ->name('FlyingPress')
                        ->icon('ti ti-rocket')
                        ->route('flying-press.settings')
                );
        });

        // Register settings panel
        PanelSectionManager::default()->beforeRendering(function (): void {
            PanelSectionManager::registerItem(
                SettingOthersPanelSection::class,
                fn () => PanelSectionItem::make('flying-press')
                    ->setTitle('FlyingPress Settings')
                    ->withIcon('ti ti-rocket')
                    ->withDescription('Configure performance optimization settings')
                    ->withPriority(50)
                    ->withRoute('flying-press.settings')
            );
        });

        // Register middleware for advanced optimization (runs after Botble's optimization)
        $this->app['router']->pushMiddlewareToGroup('web', FlyingPressOptimizationMiddleware::class);

        // Listen for route matched event to initialize optimization
        Event::listen(RouteMatched::class, function () {
            $this->initializeOptimization();
        });

        // Only register hooks for frontend requests
        $this->app->booted(function (): void {
            if (!request()->is('admin/*') && !request()->is('api/*')) {
                $this->app->register(HookServiceProvider::class);
            }
        });
    }

    protected function initializeOptimization(): void
    {
        // Initialize license service (always returns active)
        $licenseService = $this->app->make(LicenseService::class);
        $licenseService->initialize();

        // Initialize optimization services if license is active
        if ($licenseService->isActive()) {
            $optimizationService = $this->app->make(OptimizationService::class);
            $optimizationService->initialize();

            $cacheService = $this->app->make(CacheService::class);
            $cacheService->initialize();
        }
    }
}
