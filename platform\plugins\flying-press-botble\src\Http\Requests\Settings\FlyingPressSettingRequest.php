<?php

namespace Shaqi\FlyingPress\Http\Requests\Settings;

use <PERSON>haqi\Base\Rules\OnOffRule;
use <PERSON>haqi\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class FlyingPressSettingRequest extends Request
{
    public function rules(): array
    {
        return [
            // Advanced CSS & JavaScript Optimization (complementing Botble's built-in)
            'flying_press_css_rucss' => new OnOffRule(),
            'flying_press_js_delay_method' => [
                'nullable',
                'string',
                Rule::in(['background', 'interaction', 'timer', 'viewport']),
            ],
            'flying_press_js_delay_third_party' => new OnOffRule(),
            'flying_press_css_js_self_host_third_party' => new OnOffRule(),

            // Advanced Image & Media Optimization (beyond basic lazy loading)
            'flying_press_properly_size_images' => new OnOffRule(),
            'flying_press_youtube_placeholder' => new OnOffRule(),
            'flying_press_self_host_gravatars' => new OnOffRule(),
            'flying_press_webp_conversion' => new OnOffRule(),

            // Font Optimization
            'flying_press_fonts_preload' => new OnOffRule(),
            'flying_press_fonts_optimize_google' => new OnOffRule(),
            'flying_press_fonts_display_swap' => new OnOffRule(),

            // Advanced Caching & Performance
            'flying_press_cache_link_prefetch' => new OnOffRule(),
            'flying_press_cache_mobile' => new OnOffRule(),
            'flying_press_cache_logged_in' => new OnOffRule(),
            'flying_press_cache_refresh' => new OnOffRule(),
            'flying_press_cache_refresh_interval' => [
                'nullable',
                'string',
                Rule::in(['1hour', '2hours', '6hours', '12hours', '24hours']),
            ],

            // CDN Settings
            'flying_press_cdn' => new OnOffRule(),
            'flying_press_cdn_type' => [
                'nullable',
                'string',
                Rule::in(['custom', 'cloudflare', 'aws', 'maxcdn']),
            ],
            'flying_press_cdn_url' => 'nullable|string|url',
            'flying_press_cdn_file_types' => [
                'nullable',
                'string',
                Rule::in(['all', 'images', 'css_js', 'custom']),
            ],
        ];
    }
}
