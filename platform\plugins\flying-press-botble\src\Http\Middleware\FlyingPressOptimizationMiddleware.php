<?php

namespace <PERSON><PERSON>qi\FlyingPress\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use <PERSON><PERSON>qi\FlyingPress\Services\CacheService;
use <PERSON><PERSON>qi\FlyingPress\Services\OptimizationService;

class FlyingPressOptimizationMiddleware
{
    protected CacheService $cacheService;
    protected OptimizationService $optimizationService;

    public function __construct(CacheService $cacheService, OptimizationService $optimizationService)
    {
        $this->cacheService = $cacheService;
        $this->optimizationService = $optimizationService;
    }

    public function handle(Request $request, Closure $next)
    {
        // Skip optimization for admin routes
        if ($request->is('admin/*') || $request->is('api/*')) {
            return $next($request);
        }

        // Skip if license is not active
        if (!setting('flying_press_license_active', false)) {
            return $next($request);
        }

        // Check if we should serve from cache (only if advanced caching is enabled)
        if (setting('flying_press_cache_link_prefetch', false) && $this->cacheService->shouldCache()) {
            $cachedContent = $this->cacheService->getCachedPageContent($request->fullUrl());
            if ($cachedContent) {
                return response($cachedContent)
                    ->header('X-FlyingPress-Cache', 'HIT')
                    ->header('X-FlyingPress-Advanced', 'true')
                    ->header('Cache-Control', 'public, max-age=3600');
            }
        }

        $response = $next($request);

        // Only apply advanced optimizations to HTML responses
        // Let Botble's optimization package handle basic optimizations first
        if ($response instanceof Response && $this->isHtmlResponse($response)) {
            $content = $response->getContent();

            // Apply advanced optimizations that complement Botble's built-in optimization
            $optimizedContent = $this->optimizeContent($content);

            $response->setContent($optimizedContent);

            // Cache the optimized content (only if advanced caching is enabled)
            if (setting('flying_press_cache_link_prefetch', false) && $this->cacheService->shouldCache()) {
                $this->cacheService->cachePageContent($request->fullUrl(), $optimizedContent);
                $response->header('X-FlyingPress-Cache', 'MISS');
            }

            // Add performance headers
            $response->header('X-FlyingPress-Advanced', 'true');
        }

        return $response;
    }

    protected function isHtmlResponse(Response $response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');
        return strpos($contentType, 'text/html') !== false || empty($contentType);
    }

    protected function optimizeContent(string $content): string
    {
        // Apply advanced optimizations that complement Botble's built-in optimization
        $content = $this->optimizationService->optimizeContent($content);

        // Advanced unused CSS removal (only if enabled and Botble's inline CSS is active)
        if (setting('flying_press_css_rucss', false)) {
            $content = $this->removeUnusedCss($content);
        }

        // Advanced third-party script management
        if (setting('flying_press_js_delay_third_party', false)) {
            $content = $this->optimizeThirdPartyScripts($content);
        }

        return $content;
    }

    protected function optimizeThirdPartyScripts(string $content): string
    {
        // Delay third-party scripts like analytics, social widgets, etc.
        $thirdPartyDomains = [
            'google-analytics.com',
            'googletagmanager.com',
            'facebook.net',
            'twitter.com',
            'linkedin.com',
            'youtube.com',
            'vimeo.com'
        ];

        foreach ($thirdPartyDomains as $domain) {
            $content = preg_replace_callback(
                '/<script[^>]*src=["\'][^"\']*' . preg_quote($domain, '/') . '[^"\']*["\'][^>]*><\/script>/i',
                function ($matches) {
                    $script = $matches[0];
                    // Add data attribute to delay loading
                    return str_replace('<script', '<script data-flying-press-delay="true"', $script);
                },
                $content
            );
        }

        return $content;
    }

    protected function removeUnusedCss(string $content): string
    {
        // This is a simplified implementation
        // In a real scenario, you would analyze which CSS rules are actually used
        return $content;
    }
}
