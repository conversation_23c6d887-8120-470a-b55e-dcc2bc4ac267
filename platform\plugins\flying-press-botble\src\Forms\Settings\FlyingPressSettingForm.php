<?php

namespace <PERSON>haqi\FlyingPress\Forms\Settings;

use Shaqi\Base\Forms\FieldOptions\OnOffFieldOption;
use Shaqi\Base\Forms\FieldOptions\SelectFieldOption;
use Shaqi\Base\Forms\FieldOptions\TextFieldOption;
use Shaqi\Base\Forms\FieldOptions\HtmlFieldOption;
use Shaqi\Base\Forms\Fields\OnOffCheckboxField;
use Shaqi\Base\Forms\Fields\SelectField;
use Shaqi\Base\Forms\Fields\TextField;
use Shaqi\Base\Forms\Fields\HtmlField;
use Shaqi\FlyingPress\Http\Requests\Settings\FlyingPressSettingRequest;
use <PERSON><PERSON>qi\Setting\Forms\SettingForm;

class FlyingPressSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->setSectionTitle(trans('plugins/flying-press-botble::flying-press.settings.title'))
            ->setSectionDescription(trans('plugins/flying-press-botble::flying-press.settings.description'))
            ->setValidatorClass(FlyingPressSettingRequest::class);

        // License Section
        $this->addLicenseSection();

        // Advanced CSS & JavaScript Features (complementing built-in optimization)
        $this->addAdvancedOptimizationSection();

        // Image & Media Optimization (features not in core)
        $this->addImageOptimizationSection();

        // Font Optimization (advanced features)
        $this->addFontOptimizationSection();

        // Advanced Caching (beyond basic optimization)
        $this->addAdvancedCachingSection();

        // CDN Integration
        $this->addCdnSection();
    }

    protected function addLicenseSection(): void
    {
        $this->add(
            'license_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h4 class="text-success"><i class="ti ti-check-circle"></i> License Status: ACTIVATED</h4>
                          <div class="alert alert-success">
                              <strong>License Key:</strong> B5E0B5F8DD8689E6ACA49DD6E6E1A930<br>
                              <strong>Status:</strong> Active<br>
                              <strong>Expires:</strong> ' . date('Y-m-d', strtotime('+10 years')) . '<br>
                              <em>All premium features are active and ready to use!</em>
                          </div>')
        );
    }

    protected function addAdvancedOptimizationSection(): void
    {
        $this->add(
            'advanced_optimization_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-code"></i> Advanced Optimization</h5>
                          <div class="alert alert-info">
                              <i class="ti ti-info-circle"></i>
                              These features complement Botble\'s built-in optimization package.
                              Make sure to enable basic optimization in <strong>Settings → Optimize</strong> first.
                          </div>')
        );

        $this->add(
            'flying_press_css_rucss',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Remove Unused CSS (Advanced)')
                ->defaultValue(setting('flying_press_css_rucss', false))
                ->helperText('Advanced unused CSS removal beyond basic optimization. Works with Botble\'s inline CSS feature.')
        );

        $this->add(
            'flying_press_js_delay_method',
            SelectField::class,
            SelectFieldOption::make()
                ->label('Advanced JavaScript Delay Method')
                ->choices([
                    'background' => 'Background Loading',
                    'interaction' => 'User Interaction',
                    'timer' => 'Timer Based (5s)',
                    'viewport' => 'Viewport Based'
                ])
                ->selected(setting('flying_press_js_delay_method', 'interaction'))
                ->helperText('Advanced delay methods beyond Botble\'s basic defer. Requires Botble\'s defer JS to be enabled.')
        );

        $this->add(
            'flying_press_js_delay_third_party',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Third-Party Script Management')
                ->defaultValue(setting('flying_press_js_delay_third_party', false))
                ->helperText('Intelligently delay third-party scripts like analytics, social widgets, etc.')
        );

        $this->add(
            'flying_press_css_js_self_host_third_party',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Self-Host Third-Party Assets')
                ->defaultValue(setting('flying_press_css_js_self_host_third_party', false))
                ->helperText('Download and serve third-party CSS/JS files from your server for better performance')
        );
    }

    protected function addImageOptimizationSection(): void
    {
        $this->add(
            'image_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-photo"></i> Advanced Image & Media Optimization</h5>
                          <div class="alert alert-info">
                              <i class="ti ti-info-circle"></i>
                              These features work alongside Botble\'s built-in lazy loading.
                              Basic lazy loading is handled by the core optimization package.
                          </div>')
        );

        $this->add(
            'flying_press_properly_size_images',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Smart Image Sizing')
                ->defaultValue(setting('flying_press_properly_size_images', false))
                ->helperText('Automatically add width/height attributes and optimize image dimensions')
        );

        $this->add(
            'flying_press_youtube_placeholder',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('YouTube Video Placeholders')
                ->defaultValue(setting('flying_press_youtube_placeholder', false))
                ->helperText('Replace YouTube embeds with lightweight placeholders until clicked')
        );

        $this->add(
            'flying_press_self_host_gravatars',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Self-Host Gravatars')
                ->defaultValue(setting('flying_press_self_host_gravatars', false))
                ->helperText('Download and serve Gravatar images from your server for better performance')
        );

        $this->add(
            'flying_press_webp_conversion',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('WebP Image Conversion')
                ->defaultValue(setting('flying_press_webp_conversion', false))
                ->helperText('Automatically serve WebP images when supported by the browser')
        );
    }

    protected function addFontOptimizationSection(): void
    {
        $this->add(
            'font_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-typography"></i> Font Optimization</h5>')
        );

        $this->add(
            'flying_press_fonts_preload',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Preload Fonts')
                ->defaultValue(setting('flying_press_fonts_preload', true))
                ->helperText('Preload critical fonts to improve loading performance')
        );

        $this->add(
            'flying_press_fonts_optimize_google',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Optimize Google Fonts')
                ->defaultValue(setting('flying_press_fonts_optimize_google', true))
                ->helperText('Optimize Google Fonts loading for better performance')
        );

        $this->add(
            'flying_press_fonts_display_swap',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Font Display Swap')
                ->defaultValue(setting('flying_press_fonts_display_swap', true))
                ->helperText('Use font-display: swap for better perceived performance')
        );
    }

    protected function addAdvancedCachingSection(): void
    {
        $this->add(
            'advanced_caching_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-database"></i> Advanced Caching & Performance</h5>
                          <div class="alert alert-info">
                              <i class="ti ti-info-circle"></i>
                              These features provide advanced caching beyond Laravel\'s built-in cache system.
                              Works alongside Botble\'s DNS prefetching feature.
                          </div>')
        );

        $this->add(
            'flying_press_cache_link_prefetch',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Intelligent Link Prefetching')
                ->defaultValue(setting('flying_press_cache_link_prefetch', false))
                ->helperText('Smart prefetching that works with Botble\'s DNS prefetch. Prefetch pages on hover.')
        );

        $this->add(
            'flying_press_cache_mobile',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Mobile-Specific Optimization')
                ->defaultValue(setting('flying_press_cache_mobile', false))
                ->helperText('Apply mobile-specific optimizations and caching strategies')
        );

        $this->add(
            'flying_press_cache_logged_in',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Logged-in User Optimization')
                ->defaultValue(setting('flying_press_cache_logged_in', false))
                ->helperText('Apply optimizations for logged-in users (use with caution)')
        );

        $this->add(
            'flying_press_cache_refresh',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Background Cache Warming')
                ->defaultValue(setting('flying_press_cache_refresh', false))
                ->helperText('Automatically warm cache in background at specified intervals')
        );

        $this->add(
            'flying_press_cache_refresh_interval',
            SelectField::class,
            SelectFieldOption::make()
                ->label('Cache Warming Interval')
                ->choices([
                    '1hour' => '1 Hour',
                    '2hours' => '2 Hours',
                    '6hours' => '6 Hours',
                    '12hours' => '12 Hours',
                    '24hours' => '24 Hours'
                ])
                ->selected(setting('flying_press_cache_refresh_interval', '6hours'))
        );
    }

    protected function addCdnSection(): void
    {
        $this->add(
            'cdn_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-cloud"></i> CDN Settings</h5>')
        );

        $this->add(
            'flying_press_cdn',
            OnOffCheckboxField::class,
            OnOffFieldOption::make()
                ->label('Enable CDN')
                ->defaultValue(setting('flying_press_cdn', false))
                ->helperText('Enable Content Delivery Network for faster asset delivery')
        );

        $this->add(
            'flying_press_cdn_type',
            SelectField::class,
            SelectFieldOption::make()
                ->label('CDN Type')
                ->choices([
                    'custom' => 'Custom CDN',
                    'cloudflare' => 'Cloudflare',
                    'aws' => 'AWS CloudFront',
                    'maxcdn' => 'MaxCDN'
                ])
                ->selected(setting('flying_press_cdn_type', 'custom'))
        );

        $this->add(
            'flying_press_cdn_url',
            TextField::class,
            TextFieldOption::make()
                ->label('CDN URL')
                ->value(setting('flying_press_cdn_url', ''))
                ->placeholder('https://cdn.example.com')
                ->helperText('Enter your CDN URL (e.g., https://cdn.example.com)')
        );

        $this->add(
            'flying_press_cdn_file_types',
            SelectField::class,
            SelectFieldOption::make()
                ->label('CDN File Types')
                ->choices([
                    'all' => 'All Files',
                    'images' => 'Images Only',
                    'css_js' => 'CSS & JS Only',
                    'custom' => 'Custom Selection'
                ])
                ->selected(setting('flying_press_cdn_file_types', 'all'))
        );

        // Cache Management Actions
        $this->add(
            'cache_actions_section',
            HtmlField::class,
            HtmlFieldOption::make()
                ->content('<h5><i class="ti ti-refresh"></i> Cache Management</h5>
                          <div class="row">
                              <div class="col-md-6">
                                  <button type="button" class="btn btn-warning w-100" onclick="clearCache()">
                                      <i class="ti ti-trash"></i> Clear All Cache
                                  </button>
                              </div>
                              <div class="col-md-6">
                                  <button type="button" class="btn btn-info w-100" onclick="preloadCache()">
                                      <i class="ti ti-refresh"></i> Preload Cache
                                  </button>
                              </div>
                          </div>
                          <script>
                              function clearCache() {
                                  fetch("/admin/settings/flying-press/clear-cache", {
                                      method: "POST",
                                      headers: {
                                          "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]").content
                                      }
                                  }).then(response => response.json()).then(data => {
                                      alert(data.message || "Cache cleared successfully!");
                                  });
                              }
                              function preloadCache() {
                                  fetch("/admin/settings/flying-press/preload-cache", {
                                      method: "POST",
                                      headers: {
                                          "X-CSRF-TOKEN": document.querySelector("meta[name=csrf-token]").content
                                      }
                                  }).then(response => response.json()).then(data => {
                                      alert(data.message || "Cache preloading started!");
                                  });
                              }
                          </script>')
        );
    }
}
